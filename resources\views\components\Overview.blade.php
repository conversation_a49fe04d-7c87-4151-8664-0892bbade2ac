<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 px-4 sm:px-6 lg:px-8 pt-[200px] pb-12 max-w-7xl mx-auto">
    <div class="flex flex-col">
        <div class="bg-gray-100 rounded-lg overflow-hidden shadow-lg aspect-video w-full h-[600px] mb-4">

            <model-viewer alt="3D Model" src="{{ asset('') }}" auto-rotate camera-controls ar shadow-intensity="1"
                class="w-full h-full">

            </model-viewer>
        </div>

        <div class="grid grid-cols-4 gap-2">

            <div
                class="bg-gray-100 h-24 sm:h-32 rounded-md overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all duration-200 group">
                <img src="{{ asset('frontend-images/dress1.png') }}" alt="Product thumbnail 1"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
            </div>
            <div
                class="bg-gray-100 h-24 sm:h-32 rounded-md overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all duration-200 group">
                <img src="{{ asset('frontend-images/dress2.png') }}" alt="Product thumbnail 2"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
            </div>
            <div
                class="bg-gray-100 h-24 sm:h-32 rounded-md overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all duration-200 group">
                <img src="{{ asset('frontend-images/dress3.png') }}" alt="Product thumbnail 3"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
            </div>
            <div
                class="bg-gray-100 h-24 sm:h-32 rounded-md overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all duration-200 group">
                <img src="{{ asset('frontend-images/dress4.png') }}" alt="Product thumbnail 4"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
            </div>

        </div>
    </div>

    <div class="flex flex-col pt-8 lg:pt-0">


        <div class="pb-6 border-b border-gray-200 mb-6">
            <h1 class="text-4xl sm:text-5xl font-extrabold text-gray-900"
                style="font-family: 'Playfair Display', serif;">
                Red Dress
            </h1>
        </div>

        <div class="space-y-4 mb-8">

            <p class="text-gray-700 leading-relaxed">
                This is a red dress suitable for prom, debut, or formal events.
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Inclusions:</strong> Gown, Garment Bag, Hanger
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Type:</strong>Gown
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Style:</strong> Ball Gown
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Occasions:</strong> Weddings, Prom, Gala
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Color:</strong> White
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Size:</strong> M (Medium)
            </p>
            <p class="text-gray-700">
                <strong class="font-semibold">Measurements (inches):</strong> Bust: 36, Waist: 28, Hips: 38
            </p>

        </div>

        <div class="flex justify-center md:justify-start mb-8">

            <button id="inquireButton"
                class="bg-purple-600 text-white text-xl sm:text-2xl px-8 py-4 w-full md:w-auto rounded-lg shadow-md
            hover:bg-purple-700 hover:shadow-lg transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                Inquire Now
            </button>
        </div>

        <div class="border-t border-gray-200 pt-6">
            <p class="text-gray-700">
                <strong class="font-semibold">Sold by:</strong> DressUp Davao Boutique
            </p>
            <p class="text-gray-600 text-sm mt-1">
                Located at Example St., Davao City, Davao Del Sur
            </p>
            <a href="#" class="text-purple-600 hover:underline text-sm mt-2 block">
                View Shop Profile
            </a>

        </div>
    </div>
</div>