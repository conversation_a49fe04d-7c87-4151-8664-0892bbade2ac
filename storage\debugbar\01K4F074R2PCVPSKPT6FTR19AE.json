{"__meta": {"id": "01K4F074R2PCVPSKPT6FTR19AE", "datetime": "2025-09-06 07:52:54", "utime": **********.787242, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.272336, "end": **********.78726, "duration": 0.5149240493774414, "duration_str": "515ms", "measures": [{"label": "Booting", "start": **********.272336, "relative_start": 0, "end": **********.718562, "relative_end": **********.718562, "duration": 0.****************, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.718576, "relative_start": 0.****************, "end": **********.787262, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "68.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.749402, "relative_start": 0.****************, "end": **********.753612, "relative_end": **********.753612, "duration": 0.004209995269775391, "duration_str": "4.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.784229, "relative_start": 0.****************, "end": **********.784955, "relative_end": **********.784955, "duration": 0.0007259845733642578, "duration_str": "726μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.784979, "relative_start": 0.****************, "end": **********.785009, "relative_end": **********.785009, "duration": 2.9802322387695312e-05, "duration_str": "30μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.23.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1130}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 948}], "start": **********.778729, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "dress_up_davao", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/livewire.js?id=df3a17f2", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "515ms", "peak_memory": "38MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-593422210 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593422210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-84433042 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-84433042\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-31220207 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Brave&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/admin/product-images</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlU3K3NrTFVSSjVjUDlhOWIvbGhDTVE9PSIsInZhbHVlIjoiekFpRWN5QVNlb2V3N2M0QjRrR0pybDFWcTZ6UkI3TEV3ZGkxMEtxaFAvRjlsVUduR2JuSlVDb21tbzJsZ2tyaERndmRFSUV4aC9vY2NjdkhHSUJkb2NTaFdaVndCeVVRcjh3cG1YMHFlU0F0TjNTOHd4dUt2ZmNuU05FQ3N5TFQiLCJtYWMiOiJlNjFlZWQwM2M1OGQ1ZWNhYmQyNTc2MjEzZjEwOWVkN2RiNjkyMGI2NGJkYThkODkwYWIzNGJkMmQ0NjJkOGJmIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IktEelZsSDBDSkFLNVdmV21BQ0p5b1E9PSIsInZhbHVlIjoiYzNlaER4ODV4SldyUWFkVFpLYnRjS3BpWjZNMXdiMTdYVFJnYzRLdDE1Q000bnBVV2VuRzl2S01wdFh0UVF1Zkh1T3RDTGRiMUNaRUpwRjI3Mm5ZaGhvdHJscG5xQmZ3cUlST0lTVllUbjlKSVBGVWR2Vmd6c1dRUm1OUU9pTXIiLCJtYWMiOiIxZmMwNzhjOTZjZjg1MTNmZTYyZmQ3ZGM0ZDdjMzA4MGM5NDQ0NWU2YjE5OTRiMDlkYWM5MTc5MjJkN2Q2MTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31220207\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-957990424 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlU3K3NrTFVSSjVjUDlhOWIvbGhDTVE9PSIsInZhbHVlIjoiekFpRWN5QVNlb2V3N2M0QjRrR0pybDFWcTZ6UkI3TEV3ZGkxMEtxaFAvRjlsVUduR2JuSlVDb21tbzJsZ2tyaERndmRFSUV4aC9vY2NjdkhHSUJkb2NTaFdaVndCeVVRcjh3cG1YMHFlU0F0TjNTOHd4dUt2ZmNuU05FQ3N5TFQiLCJtYWMiOiJlNjFlZWQwM2M1OGQ1ZWNhYmQyNTc2MjEzZjEwOWVkN2RiNjkyMGI2NGJkYThkODkwYWIzNGJkMmQ0NjJkOGJmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IktEelZsSDBDSkFLNVdmV21BQ0p5b1E9PSIsInZhbHVlIjoiYzNlaER4ODV4SldyUWFkVFpLYnRjS3BpWjZNMXdiMTdYVFJnYzRLdDE1Q000bnBVV2VuRzl2S01wdFh0UVF1Zkh1T3RDTGRiMUNaRUpwRjI3Mm5ZaGhvdHJscG5xQmZ3cUlST0lTVllUbjlKSVBGVWR2Vmd6c1dRUm1OUU9pTXIiLCJtYWMiOiIxZmMwNzhjOTZjZjg1MTNmZTYyZmQ3ZGM0ZDdjMzA4MGM5NDQ0NWU2YjE5OTRiMDlkYWM5MTc5MjJkN2Q2MTExIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957990424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-821244129 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Sep 2026 07:52:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 13 Aug 2025 14:36:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 07:52:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821244129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1291251231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1291251231\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/livewire.js?id=df3a17f2", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}