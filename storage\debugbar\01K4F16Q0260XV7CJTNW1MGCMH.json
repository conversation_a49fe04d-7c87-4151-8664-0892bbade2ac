{"__meta": {"id": "01K4F16Q0260XV7CJTNW1MGCMH", "datetime": "2025-09-06 08:10:09", "utime": **********.284325, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": 1757146207.511474, "end": **********.284352, "duration": 1.7728781700134277, "duration_str": "1.77s", "measures": [{"label": "Booting", "start": 1757146207.511474, "relative_start": 0, "end": **********.256394, "relative_end": **********.256394, "duration": 0.***************, "duration_str": "745ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.256422, "relative_start": 0.***************, "end": **********.284355, "relative_end": 2.86102294921875e-06, "duration": 1.**************, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.308443, "relative_start": 0.****************, "end": **********.313116, "relative_end": **********.313116, "duration": 0.004673004150390625, "duration_str": "4.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::5a61f87c5330531f32df19194b30a39f", "start": **********.44635, "relative_start": 0.****************, "end": **********.44635, "relative_end": **********.44635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.275787, "relative_start": 1.****************, "end": **********.277635, "relative_end": **********.277635, "duration": 0.001847982406616211, "duration_str": "1.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.23.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "__components::5a61f87c5330531f32df19194b30a39f", "param_count": null, "params": [], "start": **********.446237, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\storage\\framework\\views/5a61f87c5330531f32df19194b30a39f.blade.php__components::5a61f87c5330531f32df19194b30a39f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fstorage%2Fframework%2Fviews%2F5a61f87c5330531f32df19194b30a39f.blade.php&line=1", "ajax": false, "filename": "5a61f87c5330531f32df19194b30a39f.blade.php", "line": "?"}}]}, "queries": {"count": 17, "nb_statements": 12, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04518, "accumulated_duration_str": "45.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.339026, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "dress_up_davao", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv' limit 1", "type": "query", "params": [], "bindings": ["Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3415978, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "dress_up_davao", "explain": null, "start_percent": 0, "width_percent": 9.872}, {"sql": "select * from `cache` where `key` in ('laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80')", "type": "query", "params": [], "bindings": ["laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}], "start": **********.472903, "duration": 0.00922, "duration_str": "9.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "dress_up_davao", "explain": null, "start_percent": 9.872, "width_percent": 20.407}, {"sql": "select * from `cache` where `key` in ('laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80:timer')", "type": "query", "params": [], "bindings": ["laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80:timer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}], "start": **********.489792, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "dress_up_davao", "explain": null, "start_percent": 30.279, "width_percent": 1.948}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80:timer', 'i:1757146268;', 1757146268)", "type": "query", "params": [], "bindings": ["laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80:timer", "i:1757146268;", 1757146268], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 213}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}, {"index": 15, "namespace": null, "name": "vendor/danharrin/livewire-rate-limiting/src/WithRateLimiting.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\danharrin\\livewire-rate-limiting\\src\\WithRateLimiting.php", "line": 38}], "start": **********.494843, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:213", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=213", "ajax": false, "filename": "DatabaseStore.php", "line": "213"}, "connection": "dress_up_davao", "explain": null, "start_percent": 32.227, "width_percent": 7.503}, {"sql": "select * from `cache` where `key` in ('laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80')", "type": "query", "params": [], "bindings": ["laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}], "start": **********.503227, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "dress_up_davao", "explain": null, "start_percent": 39.73, "width_percent": 1.771}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80', 'i:0;', 1757146268)", "type": "query", "params": [], "bindings": ["laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80", "i:0;", 1757146268], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 213}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 168}], "start": **********.50814, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:213", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=213", "ajax": false, "filename": "DatabaseStore.php", "line": "213"}, "connection": "dress_up_davao", "explain": null, "start_percent": 41.501, "width_percent": 6.197}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 263}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 234}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.520818, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:263", "source": {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=263", "ajax": false, "filename": "DatabaseStore.php", "line": "263"}, "connection": "dress_up_davao", "explain": null, "start_percent": 47.698, "width_percent": 0}, {"sql": "select * from `cache` where `key` = 'laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80' limit 1 for update", "type": "query", "params": [], "bindings": ["laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 267}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 263}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.521033, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:267", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=267", "ajax": false, "filename": "DatabaseStore.php", "line": "267"}, "connection": "dress_up_davao", "explain": null, "start_percent": 47.698, "width_percent": 2.036}, {"sql": "update `cache` set `value` = 'i:1;' where `key` = 'laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80'", "type": "query", "params": [], "bindings": ["i:1;", "laravel-cache-livewire-rate-limiter:de9f3a67e86b60f70473c387d7426235d7b31f80"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 292}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 263}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.5258, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:292", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 292}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=292", "ajax": false, "filename": "DatabaseStore.php", "line": "292"}, "connection": "dress_up_davao", "explain": null, "start_percent": 49.734, "width_percent": 2.568}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 263}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 234}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.539124, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:263", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=263", "ajax": false, "filename": "DatabaseStore.php", "line": "263"}, "connection": "dress_up_davao", "explain": null, "start_percent": 52.302, "width_percent": 0}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, {"index": 11, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 87}, {"index": 12, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 48}, {"index": 13, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 76}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.580678, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Register.php:80", "source": {"index": 10, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FFilament%2FPages%2FAuth%2FRegister.php&line=80", "ajax": false, "filename": "Register.php", "line": "80"}, "connection": "dress_up_davao", "explain": null, "start_percent": 52.302, "width_percent": 0}, {"sql": "insert into `users` (`name`, `email`, `password`, `role`, `updated_at`, `created_at`) values ('admin', '<EMAIL>', '$2y$12$GyJiuMCWtcmUB6O1y7U69u6urxKIGgIopVFdFXJ/8w4gE1Zgpe5wi', 'Admin', '2025-09-06 08:10:09', '2025-09-06 08:10:09')", "type": "query", "params": [], "bindings": ["admin", "<EMAIL>", "$2y$12$GyJiuMCWtcmUB6O1y7U69u6urxKIGgIopVFdFXJ/8w4gE1Zgpe5wi", "Admin", "2025-09-06 08:10:09", "2025-09-06 08:10:09"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 87}, {"index": 25, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 87}, {"index": 27, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 76}], "start": **********.202889, "duration": 0.00882, "duration_str": "8.82ms", "memory": 0, "memory_str": null, "filename": "Register.php:87", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FFilament%2FPages%2FAuth%2FRegister.php&line=87", "ajax": false, "filename": "Register.php", "line": "87"}, "connection": "dress_up_davao", "explain": null, "start_percent": 52.302, "width_percent": 19.522}, {"sql": "insert into `shops` (`user_id`, `shop_name`, `shop_address`, `shop_description`, `updated_at`, `created_at`) values (1, 'Admin Rental Shop', 'Admin Street Davao City ', null, '2025-09-06 08:10:09', '2025-09-06 08:10:09')", "type": "query", "params": [], "bindings": [1, "Admin Rental Shop", "Admin Street Davao City ", null, "2025-09-06 08:10:09", "2025-09-06 08:10:09"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 93}, {"index": 25, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 87}, {"index": 27, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 76}], "start": **********.221322, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "Register.php:93", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FFilament%2FPages%2FAuth%2FRegister.php&line=93", "ajax": false, "filename": "Register.php", "line": "93"}, "connection": "dress_up_davao", "explain": null, "start_percent": 71.824, "width_percent": 4.98}, {"sql": "insert into `subscriptions` (`user_id`, `updated_at`, `created_at`) values (1, '2025-09-06 08:10:09', '2025-09-06 08:10:09')", "type": "query", "params": [], "bindings": [1, "2025-09-06 08:10:09", "2025-09-06 08:10:09"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 100}, {"index": 25, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 87}, {"index": 27, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 76}], "start": **********.228898, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "Register.php:100", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FFilament%2FPages%2FAuth%2FRegister.php&line=100", "ajax": false, "filename": "Register.php", "line": "100"}, "connection": "dress_up_davao", "explain": null, "start_percent": 76.804, "width_percent": 4.095}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, {"index": 10, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 87}, {"index": 11, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 48}, {"index": 12, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 76}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.246298, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Register.php:80", "source": {"index": 9, "namespace": null, "name": "app/Filament/Pages/Auth/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Pages\\Auth\\Register.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FFilament%2FPages%2FAuth%2FRegister.php&line=80", "ajax": false, "filename": "Register.php", "line": "80"}, "connection": "dress_up_davao", "explain": null, "start_percent": 80.899, "width_percent": 0}, {"sql": "delete from `sessions` where `id` = 'Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv'", "type": "query", "params": [], "bindings": ["Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 578}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 549}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Auth/Pages/Register.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Auth\\Pages\\Register.php", "line": 100}], "start": **********.25607, "duration": 0.00863, "duration_str": "8.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "dress_up_davao", "explain": null, "start_percent": 80.899, "width_percent": 19.101}]}, "models": {"data": {"App\\Models\\User": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shops": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FModels%2FShops.php&line=1", "ajax": false, "filename": "Shops.php", "line": "?"}}, "App\\Models\\Subscriptions": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FModels%2FSubscriptions.php&line=1", "ajax": false, "filename": "Subscriptions.php", "line": "?"}}}, "count": 3, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 3}}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Pages\\Auth\\Register@register<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FAuth%2FPages%2FRegister.php&line=66\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FAuth%2FPages%2FRegister.php&line=66\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Auth/Pages/Register.php:66-105</a>", "middleware": "web", "duration": "1.78s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-532167729 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-532167729\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-809430316 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tNsbUohk2X7kLhulHsyA4PdGYL6vQgV0m5CgSNtk</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"813 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;name&quot;:&quot;admin&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;password&quot;:&quot;admin123&quot;,&quot;password_confirmation&quot;:&quot;admin123&quot;,&quot;shop_name&quot;:null,&quot;shop_address&quot;:null,&quot;shop_description&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[&quot;form&quot;,&quot;content&quot;],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;NcDabt9GNDWmarhHSE1o&quot;,&quot;name&quot;:&quot;app.filament.pages.auth.register&quot;,&quot;path&quot;:&quot;admin\\/register&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;bf31129fe25233f5e03890f2397dc98496e64e448c49a4f7f82e02bccb8cd463&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.shop_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Admin Rental Shop</span>\"\n        \"<span class=sf-dump-key>data.shop_address</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Admin Street Davao City </span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">register</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809430316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1146273949 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1148</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Brave&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/admin/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlRYOVlVeUkrZ3BLck5EaTRQNHo0TFE9PSIsInZhbHVlIjoiRWFoUHBpOVdweWF6OEI2NXRGaG44dU9QWFBKRE9CNjUvdGpuMmVJY3FvSEg2bU1Md1B6UUdXK2pTZi9ndUFxOUc1QUFaMlVPbWFkT0N3a04vczhrbFROUUM4amFlWDlGbVJHVjBKMmdNWUxCd0NDNFB1K3VYdDNmQ0J2d010QVkiLCJtYWMiOiJhYTFkNzIzMmY5MzYwYjZlNDA4ZDY5YzIwNGJmYjU0NzgwNTcwYTIyZjdmZTVmYjFmMDY2YWVkZjFhY2EyYzY5IiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6Imt0c0JRQlRqMHo2MjBXWTF4R1J4MVE9PSIsInZhbHVlIjoiTU1XTUNpbHBDWkdTQ1VObHE5c2pBRzdhTjVJcjh0b2R4MUJNa3NIL1Y1MmVRWEpma0M3Vkh0aHRzejgzZFcwRmE3QUxOVWY2dmtLZkJ2OGtiSm5iVEp3bjg5WHFGaXN4Tmk5UzFZOEh1bTVtM1hobWVueG9pclVReHFaNnVlS3MiLCJtYWMiOiI1OTQ0NzVkZjA0YzgxMGVhMmM2NmVhODYyODRiNzFlMWM4NmI3NDJmNTc5NTVmNjE5NGYxYjQ5MDM4ODI1ZDYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146273949\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1841403624 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tNsbUohk2X7kLhulHsyA4PdGYL6vQgV0m5CgSNtk</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841403624\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-806895546 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 08:10:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806895546\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1644916026 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4jzRZ9UMXpC50oauxLvixeHqSig7zMBrlacXNaUh</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/admin/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644916026\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}