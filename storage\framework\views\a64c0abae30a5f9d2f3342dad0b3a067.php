<div class="space-y-2">
    <div>
        <strong>Gown:</strong>
        <ul class="list-disc list-inside text-sm text-gray-700">
            <li>Length: <?php echo e($getRecord()->gown_length); ?></li>
            <li>Upper Chest: <?php echo e($getRecord()->gown_upper_chest); ?></li>
            <li>Chest: <?php echo e($getRecord()->gown_chest); ?></li>
            <li>Waist: <?php echo e($getRecord()->gown_waist); ?></li>
            <li>Hips: <?php echo e($getRecord()->gown_hips); ?></li>
        </ul>
    </div>

    <div>
        <strong>Jacket:</strong>
        <ul class="list-disc list-inside text-sm text-gray-700">
            <li>Chest: <?php echo e($getRecord()->jacket_chest); ?></li>
            <li>Length: <?php echo e($getRecord()->jacket_length); ?></li>
            <li>Shoulder: <?php echo e($getRecord()->jacket_shoulder); ?></li>
            <li>Sleeve Length: <?php echo e($getRecord()->jacket_sleeve_length); ?></li>
            <li>Sleeve Width: <?php echo e($getRecord()->jacket_sleeve_width); ?></li>
            <li>Bicep: <?php echo e($getRecord()->jacket_bicep); ?></li>
            <li>Arm Hole: <?php echo e($getRecord()->jacket_arm_hole); ?></li>
            <li>Waist: <?php echo e($getRecord()->jacket_waist); ?></li>
        </ul>
    </div>

    <div>
        <strong>Trousers:</strong>
        <ul class="list-disc list-inside text-sm text-gray-700">
            <li>Waist: <?php echo e($getRecord()->trouser_waist); ?></li>
            <li>Hip: <?php echo e($getRecord()->trouser_hip); ?></li>
            <li>Inseam: <?php echo e($getRecord()->trouser_inseam); ?></li>
            <li>Outseam: <?php echo e($getRecord()->trouser_outseam); ?></li>
            <li>Thigh: <?php echo e($getRecord()->trouser_thigh); ?></li>
            <li>Leg Opening: <?php echo e($getRecord()->trouser_leg_opening); ?></li>
            <li>Crotch: <?php echo e($getRecord()->trouser_crotch); ?></li>
        </ul>
    </div>
</div><?php /**PATH C:\Users\<USER>\dress-up-davao\resources\views/tables/columns/measurements.blade.php ENDPATH**/ ?>