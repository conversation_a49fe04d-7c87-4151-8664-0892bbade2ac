{"__meta": {"id": "01K4F0EY0K7BSSBTQ2Z0V374YJ", "datetime": "2025-09-06 07:57:10", "utime": **********.037093, "method": "GET", "uri": "/admin/product-images/3/edit", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 12, "start": *********8.850588, "end": **********.037158, "duration": 1.1865699291229248, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": *********8.850588, "relative_start": 0, "end": **********.47492, "relative_end": **********.47492, "duration": 0.****************, "duration_str": "624ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.474947, "relative_start": 0.****************, "end": **********.037163, "relative_end": 5.0067901611328125e-06, "duration": 0.**************, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.525451, "relative_start": 0.****************, "end": **********.530956, "relative_end": **********.530956, "duration": 0.005505084991455078, "duration_str": "5.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.814966, "relative_start": 0.****************, "end": **********.814966, "relative_end": **********.814966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::55a357bbd0ec6bf7e58e877a518d9959", "start": **********.834982, "relative_start": 0.***************, "end": **********.834982, "relative_end": **********.834982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::55a357bbd0ec6bf7e58e877a518d9959", "start": **********.843215, "relative_start": 0.9926269054412842, "end": **********.843215, "relative_end": **********.843215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.883886, "relative_start": 1.0332980155944824, "end": **********.883886, "relative_end": **********.883886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": **********.904149, "relative_start": 1.0535609722137451, "end": **********.904149, "relative_end": **********.904149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7d15c559e2b59f7fe21cb69e18a01cd7", "start": **********.920402, "relative_start": 1.0698139667510986, "end": **********.920402, "relative_end": **********.920402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": **********.951211, "relative_start": 1.1006228923797607, "end": **********.951211, "relative_end": **********.951211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.01527, "relative_start": 1.1646819114685059, "end": **********.015585, "relative_end": **********.015585, "duration": 0.00031495094299316406, "duration_str": "315μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.032009, "relative_start": 1.1814208030700684, "end": **********.032125, "relative_end": **********.032125, "duration": 0.00011610984802246094, "duration_str": "116μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 47027424, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.23.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.814815, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::55a357bbd0ec6bf7e58e877a518d9959", "param_count": null, "params": [], "start": **********.834865, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\storage\\framework\\views/55a357bbd0ec6bf7e58e877a518d9959.blade.php__components::55a357bbd0ec6bf7e58e877a518d9959", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fstorage%2Fframework%2Fviews%2F55a357bbd0ec6bf7e58e877a518d9959.blade.php&line=1", "ajax": false, "filename": "55a357bbd0ec6bf7e58e877a518d9959.blade.php", "line": "?"}}, {"name": "__components::55a357bbd0ec6bf7e58e877a518d9959", "param_count": null, "params": [], "start": **********.843107, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\storage\\framework\\views/55a357bbd0ec6bf7e58e877a518d9959.blade.php__components::55a357bbd0ec6bf7e58e877a518d9959", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fstorage%2Fframework%2Fviews%2F55a357bbd0ec6bf7e58e877a518d9959.blade.php&line=1", "ajax": false, "filename": "55a357bbd0ec6bf7e58e877a518d9959.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.88378, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament-panels::components.logo", "param_count": null, "params": [], "start": **********.904044, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}}, {"name": "__components::7d15c559e2b59f7fe21cb69e18a01cd7", "param_count": null, "params": [], "start": **********.920295, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\storage\\framework\\views/7d15c559e2b59f7fe21cb69e18a01cd7.blade.php__components::7d15c559e2b59f7fe21cb69e18a01cd7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fstorage%2Fframework%2Fviews%2F7d15c559e2b59f7fe21cb69e18a01cd7.blade.php&line=1", "ajax": false, "filename": "7d15c559e2b59f7fe21cb69e18a01cd7.blade.php", "line": "?"}}, {"name": "filament-panels::components.logo", "param_count": null, "params": [], "start": **********.951099, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01993, "accumulated_duration_str": "19.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.558811, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "dress_up_davao", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv' limit 1", "type": "query", "params": [], "bindings": ["Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.561671, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "dress_up_davao", "explain": null, "start_percent": 0, "width_percent": 24.887}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.589148, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "dress_up_davao", "explain": null, "start_percent": 24.887, "width_percent": 5.82}, {"sql": "select * from `product_images` where exists (select * from `products` where `product_images`.`product_id` = `products`.`product_id` and `user_id` = 1) and `product_image_id` = '3' limit 1", "type": "query", "params": [], "bindings": [1, "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Resources\\Resource\\Concerns\\HasRoutes.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.616337, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoutes.php:44", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Resources\\Resource\\Concerns\\HasRoutes.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource%2FConcerns%2FHasRoutes.php&line=44", "ajax": false, "filename": "HasRoutes.php", "line": "44"}, "connection": "dress_up_davao", "explain": null, "start_percent": 30.707, "width_percent": 6.724}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProductImages/Schemas/ProductImagesForm.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Resources\\ProductImages\\Schemas\\ProductImagesForm.php", "line": 21}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/ProductImages/ProductImagesResource.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Resources\\ProductImages\\ProductImagesResource.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/InteractsWithSchemas.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\schemas\\src\\Concerns\\InteractsWithSchemas.php", "line": 273}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/InteractsWithSchemas.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\schemas\\src\\Concerns\\InteractsWithSchemas.php", "line": 295}], "start": **********.673496, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ProductImagesForm.php:21", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProductImages/Schemas/ProductImagesForm.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\app\\Filament\\Resources\\ProductImages\\Schemas\\ProductImagesForm.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FFilament%2FResources%2FProductImages%2FSchemas%2FProductImagesForm.php&line=21", "ajax": false, "filename": "ProductImagesForm.php", "line": "21"}, "connection": "dress_up_davao", "explain": null, "start_percent": 37.431, "width_percent": 6.824}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 104}, {"index": 20, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 206}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.911218, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:104", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=104", "ajax": false, "filename": "DatabaseNotifications.php", "line": "104"}, "connection": "dress_up_davao", "explain": null, "start_percent": 44.255, "width_percent": 8.831}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoidGZEU1ZoaGFtZ3p3SzhVWnhJNHNFbUExNVE3ZzVoNXdrRGIzdEtpSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDk6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hZG1pbi9wcm9kdWN0LWltYWdlcy8zL2VkaXQiO31zOjIyOiJQSFBERUJVR0JBUl9TVEFDS19EQVRBIjthOjA6e31zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkVlNMUmNhN1c3enk4RTlrLjJ4eHdHLlNxS0dEWkhIOFpwZEF2TEUuajRrRjI4bU1mM05Dci4iO3M6NjoidGFibGVzIjthOjE6e3M6NDA6ImQ1ZDI1YjQ2NDNhZWUxZmQxM2YxYTVkMzY1ZWUwMjA3X2NvbHVtbnMiO2E6Mzp7aTowO2E6Nzp7czo0OiJ0eXBlIjtzOjY6ImNvbHVtbiI7czo0OiJuYW1lIjtzOjEyOiJwcm9kdWN0Lm5hbWUiO3M6NToibGFiZWwiO3M6MTI6IlByb2R1Y3QgTmFtZSI7czo4OiJpc0hpZGRlbiI7YjowO3M6OToiaXNUb2dnbGVkIjtiOjE7czoxMjoiaXNUb2dnbGVhYmxlIjtiOjA7czoyNDoiaXNUb2dnbGVkSGlkZGVuQnlEZWZhdWx0IjtOO31pOjE7YTo3OntzOjQ6InR5cGUiO3M6NjoiY29sdW1uIjtzOjQ6Im5hbWUiO3M6MTA6ImltYWdlX3BhdGgiO3M6NToibGFiZWwiO3M6NjoiSW1hZ2VzIjtzOjg6ImlzSGlkZGVuIjtiOjA7czo5OiJpc1RvZ2dsZWQiO2I6MTtzOjEyOiJpc1RvZ2dsZWFibGUiO2I6MDtzOjI0OiJpc1RvZ2dsZWRIaWRkZW5CeURlZmF1bHQiO047fWk6MjthOjc6e3M6NDoidHlwZSI7czo2OiJjb2x1bW4iO3M6NDoibmFtZSI7czo0OiJ0eXBlIjtzOjU6ImxhYmVsIjtzOjQ6IlR5cGUiO3M6ODoiaXNIaWRkZW4iO2I6MDtzOjk6ImlzVG9nZ2xlZCI7YjoxO3M6MTI6ImlzVG9nZ2xlYWJsZSI7YjowO3M6MjQ6ImlzVG9nZ2xlZEhpZGRlbkJ5RGVmYXVsdCI7Tjt9fX19', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36' where `id` = 'Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoidGZEU1ZoaGFtZ3p3SzhVWnhJNHNFbUExNVE3ZzVoNXdrRGIzdEtpSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDk6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hZG1pbi9wcm9kdWN0LWltYWdlcy8zL2VkaXQiO31zOjIyOiJQSFBERUJVR0JBUl9TVEFDS19EQVRBIjthOjA6e31zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkVlNMUmNhN1c3enk4RTlrLjJ4eHdHLlNxS0dEWkhIOFpwZEF2TEUuajRrRjI4bU1mM05Dci4iO3M6NjoidGFibGVzIjthOjE6e3M6NDA6ImQ1ZDI1YjQ2NDNhZWUxZmQxM2YxYTVkMzY1ZWUwMjA3X2NvbHVtbnMiO2E6Mzp7aTowO2E6Nzp7czo0OiJ0eXBlIjtzOjY6ImNvbHVtbiI7czo0OiJuYW1lIjtzOjEyOiJwcm9kdWN0Lm5hbWUiO3M6NToibGFiZWwiO3M6MTI6IlByb2R1Y3QgTmFtZSI7czo4OiJpc0hpZGRlbiI7YjowO3M6OToiaXNUb2dnbGVkIjtiOjE7czoxMjoiaXNUb2dnbGVhYmxlIjtiOjA7czoyNDoiaXNUb2dnbGVkSGlkZGVuQnlEZWZhdWx0IjtOO31pOjE7YTo3OntzOjQ6InR5cGUiO3M6NjoiY29sdW1uIjtzOjQ6Im5hbWUiO3M6MTA6ImltYWdlX3BhdGgiO3M6NToibGFiZWwiO3M6NjoiSW1hZ2VzIjtzOjg6ImlzSGlkZGVuIjtiOjA7czo5OiJpc1RvZ2dsZWQiO2I6MTtzOjEyOiJpc1RvZ2dsZWFibGUiO2I6MDtzOjI0OiJpc1RvZ2dsZWRIaWRkZW5CeURlZmF1bHQiO047fWk6MjthOjc6e3M6NDoidHlwZSI7czo2OiJjb2x1bW4iO3M6NDoibmFtZSI7czo0OiJ0eXBlIjtzOjU6ImxhYmVsIjtzOjQ6IlR5cGUiO3M6ODoiaXNIaWRkZW4iO2I6MDtzOjk6ImlzVG9nZ2xlZCI7YjoxO3M6MTI6ImlzVG9nZ2xlYWJsZSI7YjowO3M6MjQ6ImlzVG9nZ2xlZEhpZGRlbkJ5RGVmYXVsdCI7Tjt9fX19", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.017555, "duration": 0.009349999999999999, "duration_str": "9.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "dress_up_davao", "explain": null, "start_percent": 53.086, "width_percent": 46.914}]}, "models": {"data": {"App\\Models\\Products": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FModels%2FProducts.php&line=1", "ajax": false, "filename": "Products.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductImages": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fapp%2FModels%2FProductImages.php&line=1", "ajax": false, "filename": "ProductImages.php", "line": "?"}}}, "count": 4, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 4}}, "livewire": {"data": {"app.filament.resources.product-images.pages.edit-product-images #B8eNVoMiZmxINJcfV5N8": "array:4 [\n  \"data\" => array:16 [\n    \"data\" => array:7 [\n      \"product_image_id\" => 3\n      \"product_id\" => 2\n      \"image_path\" => []\n      \"type\" => \"thumbnail\"\n      \"created_at\" => \"2025-09-06T07:18:55.000000Z\"\n      \"updated_at\" => \"2025-09-06T07:18:55.000000Z\"\n      \"thumbnail\" => []\n    ]\n    \"previousUrl\" => \"http://127.0.0.1:8000/admin/product-images\"\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:1 [\n      0 => \"form\"\n    ]\n    \"parentRecord\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\ProductImages {#2369\n      #connection: \"mysql\"\n      #table: \"product_images\"\n      #primaryKey: \"product_image_id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:6 [\n        \"product_image_id\" => 3\n        \"product_id\" => 2\n        \"image_path\" => \"product-images/01K4EY8WY42MRTX87THA9Q7TVJ.jpg\"\n        \"type\" => \"thumbnail\"\n        \"created_at\" => \"2025-09-06 07:18:55\"\n        \"updated_at\" => \"2025-09-06 07:18:55\"\n      ]\n      #original: array:6 [\n        \"product_image_id\" => 3\n        \"product_id\" => 2\n        \"image_path\" => \"product-images/01K4EY8WY42MRTX87THA9Q7TVJ.jpg\"\n        \"type\" => \"thumbnail\"\n        \"created_at\" => \"2025-09-06 07:18:55\"\n        \"updated_at\" => \"2025-09-06 07:18:55\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"product_id\"\n        1 => \"image_path\"\n        2 => \"type\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.product-images.pages.edit-product-images\"\n  \"component\" => \"App\\Filament\\Resources\\ProductImages\\Pages\\EditProductImages\"\n  \"id\" => \"B8eNVoMiZmxINJcfV5N8\"\n]", "filament.livewire.topbar #1PwjwFCjPmxd89NmPN4L": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.topbar\"\n  \"component\" => \"Filament\\Livewire\\Topbar\"\n  \"id\" => \"1PwjwFCjPmxd89NmPN4L\"\n]", "filament.livewire.sidebar #gzY96M2TGixDIeRxVXUx": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.sidebar\"\n  \"component\" => \"Filament\\Livewire\\Sidebar\"\n  \"id\" => \"gzY96M2TGixDIeRxVXUx\"\n]", "filament.livewire.notifications #ANU3ISRyKHfxKM2lXAFb": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#5739\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"ANU3ISRyKHfxKM2lXAFb\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/product-images/3/edit", "action_name": "filament.admin.resources.product-images.edit", "controller_action": "App\\Filament\\Resources\\ProductImages\\Pages\\EditProductImages", "uri": "GET admin/product-images/{record}/edit", "controller": "App\\Filament\\Resources\\ProductImages\\Pages\\EditProductImages@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/product-images", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:53-61</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, Joaopaulolndev\\FilamentEditProfile\\Http\\Middleware\\SetUserThemeColor:web, Joaopaulolndev\\FilamentEditProfile\\Http\\Middleware\\SetUserLocale:web", "duration": "1.19s", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Brave&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/admin/product-images</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkV4L0owNUVLRGFzVC9PdUJNZHhmdHc9PSIsInZhbHVlIjoiZTdOb0dhaXRYRmtQTStsM1VNSm5uU2pNV3l4NEtQS3ptMjA2VkFrbXgwUmpRQ09ZRmlwUmJsSDhsVTZGWVM0MXowYVNxNHhlempBNEQ2QWtkODR1Z3MrVDNRN0JFWldrMUNiR2Nlc2h0SjhGWTNIZjc2N2w2THdOVU5Hd1E4SlIiLCJtYWMiOiJiYTU5MWIyOTEyOTgxYmJiYzIwYTZkMGU3YTNkNWI3MzNkMmUwYmRlNjhkMjdmZTY0YWRlMjQ2NzUyNjA5MmNjIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IkI0ZVg3VkxSUWFzZ2M2Y0VvOTRBRnc9PSIsInZhbHVlIjoibGIvTk54UmFDUUVyL09JUmtrTEFGNThEelNiZXhLOXhIV0h4Rm9JZVRuZ1M1Y2JhR1FxUklTZ1A4NEQzVjlrTUhmNDdxbkVWd3k1cU0wUVZBYUdpZGZwUnJ0Yng2SjNWeC9jWGZQeUUzUWwvUjhLZnRsRnFtYjRHeWxrdGdBeTYiLCJtYWMiOiIyOTJiOWEzMWI0OGJiMGZkM2VjMDA0NjVlOWUyODA2YjM3NzZhNjVmYTM3YjViOTExOWUyNDIzMjk1N2EyMDgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-340057536 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tfDSVhhamgzwK8UZxI4sEmA15Q7g5h5wkDb3tKiK</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xu6Ax4woz6ZQJVenY0UC9F1k8DHDUOjzbtRxzvxv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340057536\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-663357725 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 07:57:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlpUZmxpeDJBZnpiWlJEd3dST25qYmc9PSIsInZhbHVlIjoiazJWS1ZYaHZsdTlsS05hMWhROWNGYTR5MUs5OFJWNStHQTZsdStsemErT0hua2MrT0FnRDVjcjdhY0RGTFppT3VCUzdWUWhDa2ZGSXhBT3Q1ZW1kTWNpd2xOeUZqTEtLRlpkYS95RGdOWFNMNDZIZkp5T3VXV2JXOE1nNGVQWTYiLCJtYWMiOiJkOTRiZTY0OWVkZTk2ZDQ2MWM4MDhhMjNhYTI3MzgzYzFmZmU2MjQwNWI0NmRlY2M4NTFmNjhhMDE0YjFlOWRlIiwidGFnIjoiIn0%3D; expires=Sat, 06 Sep 2025 09:57:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel-session=eyJpdiI6IjA0NUtsVnh0b0JvRTYvcGdjQ1I5TUE9PSIsInZhbHVlIjoiK0J0TWFqWDZGaFFiOVV2RXRxMlM1QnpPRnZZY3h6c2h5VVg3RHVBQWNPWUxQbWg5djkzbGpjTStmdEtEaFloZDdPdVl5aDhHa0svN2hSejQweVhnUHJtRDhIcDZUTHMxUk16SXdRTEIzQ2g4Y05uWXBXMUg3Sk05bXFreDdaaFEiLCJtYWMiOiIwNDEwODBkMzUyOWU0MDk5MmYzNDc2YzQyZWRhMDMwMjg1MzQ3M2NmOGEyOGEwM2Y5Yzc4MmEwNjE0MGIyYzgzIiwidGFnIjoiIn0%3D; expires=Sat, 06 Sep 2025 09:57:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlpUZmxpeDJBZnpiWlJEd3dST25qYmc9PSIsInZhbHVlIjoiazJWS1ZYaHZsdTlsS05hMWhROWNGYTR5MUs5OFJWNStHQTZsdStsemErT0hua2MrT0FnRDVjcjdhY0RGTFppT3VCUzdWUWhDa2ZGSXhBT3Q1ZW1kTWNpd2xOeUZqTEtLRlpkYS95RGdOWFNMNDZIZkp5T3VXV2JXOE1nNGVQWTYiLCJtYWMiOiJkOTRiZTY0OWVkZTk2ZDQ2MWM4MDhhMjNhYTI3MzgzYzFmZmU2MjQwNWI0NmRlY2M4NTFmNjhhMDE0YjFlOWRlIiwidGFnIjoiIn0%3D; expires=Sat, 06-Sep-2025 09:57:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel-session=eyJpdiI6IjA0NUtsVnh0b0JvRTYvcGdjQ1I5TUE9PSIsInZhbHVlIjoiK0J0TWFqWDZGaFFiOVV2RXRxMlM1QnpPRnZZY3h6c2h5VVg3RHVBQWNPWUxQbWg5djkzbGpjTStmdEtEaFloZDdPdVl5aDhHa0svN2hSejQweVhnUHJtRDhIcDZUTHMxUk16SXdRTEIzQ2g4Y05uWXBXMUg3Sk05bXFreDdaaFEiLCJtYWMiOiIwNDEwODBkMzUyOWU0MDk5MmYzNDc2YzQyZWRhMDMwMjg1MzQ3M2NmOGEyOGEwM2Y5Yzc4MmEwNjE0MGIyYzgzIiwidGFnIjoiIn0%3D; expires=Sat, 06-Sep-2025 09:57:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663357725\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-188914440 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tfDSVhhamgzwK8UZxI4sEmA15Q7g5h5wkDb3tKiK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/admin/product-images/3/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$VSLRca7W7zy8E9k.2xxwG.SqKGDZHH8ZpdAvLE.j4kF28mMf3NCr.</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>d5d25b4643aee1fd13f1a5d365ee0207_columns</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">image_path</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Images</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188914440\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/product-images/3/edit", "action_name": "filament.admin.resources.product-images.edit", "controller_action": "App\\Filament\\Resources\\ProductImages\\Pages\\EditProductImages"}, "badge": null}}