<?php

namespace App\Livewire;

use App\Models\Shops;
use Filament\Forms\Components\{TextInput, Textarea, FileUpload, Hidden};
use Filament\Notifications\Notification;
use Filament\Widgets\FormWidget;
use Filament\Forms;
use Illuminate\Contracts\View\View;

class ShopDetailsForm extends FormWidget
{
    protected static string $view = 'livewire.shop-details-form';

    public function getFormSchema(): array
    {
        return [
            Hidden::make('user_id')
                ->default(auth()->id()),
            TextInput::make('shop_name')
                ->label('Shop Name')
                ->required(),
            TextInput::make('shop_address')
                ->label('Shop Address'),
            Textarea::make('shop_description')
                ->label('Shop Description'),
            TextInput::make('shop_slug')
                ->label('Shop Slug'),
            FileUpload::make('shop_logo')
                ->label('Shop Logo')
                ->directory('shop-logos')
                ->image()
                ->maxSize(2048),
            Textarea::make('shop_policy')
                ->label('Shop Policy'),
        ];
    }

    public function save(): void
    {
        Shops::updateOrCreate(
            ['user_id' => auth()->id()],
            $this->form->getState()
        );

        Notification::make()
            ->title('Shop details updated!')
            ->success()
            ->send();
    }
}
