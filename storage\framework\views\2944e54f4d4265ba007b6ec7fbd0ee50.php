<script type="module" src="https://ajax.googleapis.com/ajax/libs/model-viewer/4.0.0/model-viewer.min.js"></script>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Product Overview</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-white min-h-screen flex flex-col">
    <main class="flex-grow">
        <?php if (isset($component)) { $__componentOriginal08cf8b3632d5e9dfc173bb3501abd94f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal08cf8b3632d5e9dfc173bb3501abd94f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.Navbar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('Navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal08cf8b3632d5e9dfc173bb3501abd94f)): ?>
<?php $attributes = $__attributesOriginal08cf8b3632d5e9dfc173bb3501abd94f; ?>
<?php unset($__attributesOriginal08cf8b3632d5e9dfc173bb3501abd94f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal08cf8b3632d5e9dfc173bb3501abd94f)): ?>
<?php $component = $__componentOriginal08cf8b3632d5e9dfc173bb3501abd94f; ?>
<?php unset($__componentOriginal08cf8b3632d5e9dfc173bb3501abd94f); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal72532259dd267bf5d32f2cb8d7a591ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal72532259dd267bf5d32f2cb8d7a591ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.Overview','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('Overview'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal72532259dd267bf5d32f2cb8d7a591ab)): ?>
<?php $attributes = $__attributesOriginal72532259dd267bf5d32f2cb8d7a591ab; ?>
<?php unset($__attributesOriginal72532259dd267bf5d32f2cb8d7a591ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal72532259dd267bf5d32f2cb8d7a591ab)): ?>
<?php $component = $__componentOriginal72532259dd267bf5d32f2cb8d7a591ab; ?>
<?php unset($__componentOriginal72532259dd267bf5d32f2cb8d7a591ab); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginala62ad50360dcdbe545397dd07f8acaff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala62ad50360dcdbe545397dd07f8acaff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.Chatwindow','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('Chatwindow'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala62ad50360dcdbe545397dd07f8acaff)): ?>
<?php $attributes = $__attributesOriginala62ad50360dcdbe545397dd07f8acaff; ?>
<?php unset($__attributesOriginala62ad50360dcdbe545397dd07f8acaff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala62ad50360dcdbe545397dd07f8acaff)): ?>
<?php $component = $__componentOriginala62ad50360dcdbe545397dd07f8acaff; ?>
<?php unset($__componentOriginala62ad50360dcdbe545397dd07f8acaff); ?>
<?php endif; ?>

        <?php if (isset($component)) { $__componentOriginal0f15eef1403534b3b500623a57a06627 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0f15eef1403534b3b500623a57a06627 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.Inquire','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('Inquire'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0f15eef1403534b3b500623a57a06627)): ?>
<?php $attributes = $__attributesOriginal0f15eef1403534b3b500623a57a06627; ?>
<?php unset($__attributesOriginal0f15eef1403534b3b500623a57a06627); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0f15eef1403534b3b500623a57a06627)): ?>
<?php $component = $__componentOriginal0f15eef1403534b3b500623a57a06627; ?>
<?php unset($__componentOriginal0f15eef1403534b3b500623a57a06627); ?>
<?php endif; ?>
    </main>
</body>

</html><?php /**PATH C:\Users\<USER>\dress-up-davao\resources\views/ProductOverview.blade.php ENDPATH**/ ?>