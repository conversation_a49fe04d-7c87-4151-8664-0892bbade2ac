{"__meta": {"id": "01K4F0C743AB72TY4ET0A26HQE", "datetime": "2025-09-06 07:55:41", "utime": **********.060901, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 10, "start": **********.558672, "end": **********.060923, "duration": 0.502251148223877, "duration_str": "502ms", "measures": [{"label": "Booting", "start": **********.558672, "relative_start": 0, "end": **********.983728, "relative_end": **********.983728, "duration": 0.*****************, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.983744, "relative_start": 0.*****************, "end": **********.060926, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "77.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.012035, "relative_start": 0.*****************, "end": **********.015357, "relative_end": **********.015357, "duration": 0.003322124481201172, "duration_str": "3.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.043189, "relative_start": 0.*****************, "end": **********.058021, "relative_end": **********.058021, "duration": 0.014832019805908203, "duration_str": "14.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: Home", "start": **********.045923, "relative_start": 0.*****************, "end": **********.045923, "relative_end": **********.045923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.Navbar", "start": **********.048895, "relative_start": 0.****************, "end": **********.048895, "relative_end": **********.048895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.Hero", "start": **********.055073, "relative_start": 0.4964010715484619, "end": **********.055073, "relative_end": **********.055073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.Category", "start": **********.055876, "relative_start": 0.***************, "end": **********.055876, "relative_end": **********.055876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.ProductCards", "start": **********.05661, "relative_start": 0.****************, "end": **********.05661, "relative_end": **********.05661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.Chatwindow", "start": **********.057385, "relative_start": 0.*****************, "end": **********.057385, "relative_end": **********.057385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 38197072, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.23.1", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "Home", "param_count": null, "params": [], "start": **********.045824, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/Home.blade.phpHome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2FHome.blade.php&line=1", "ajax": false, "filename": "Home.blade.php", "line": "?"}}, {"name": "components.Navbar", "param_count": null, "params": [], "start": **********.048827, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/components/Navbar.blade.phpcomponents.Navbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fcomponents%2FNavbar.blade.php&line=1", "ajax": false, "filename": "Navbar.blade.php", "line": "?"}}, {"name": "components.Hero", "param_count": null, "params": [], "start": **********.05501, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/components/Hero.blade.phpcomponents.Hero", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fcomponents%2FHero.blade.php&line=1", "ajax": false, "filename": "Hero.blade.php", "line": "?"}}, {"name": "components.Category", "param_count": null, "params": [], "start": **********.055758, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/components/Category.blade.phpcomponents.Category", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fcomponents%2FCategory.blade.php&line=1", "ajax": false, "filename": "Category.blade.php", "line": "?"}}, {"name": "components.ProductCards", "param_count": null, "params": [], "start": **********.056545, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/components/ProductCards.blade.phpcomponents.ProductCards", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fcomponents%2FProductCards.blade.php&line=1", "ajax": false, "filename": "ProductCards.blade.php", "line": "?"}}, {"name": "components.Chatwindow", "param_count": null, "params": [], "start": **********.057318, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\dress-up-davao\\resources\\views/components/Chatwindow.blade.phpcomponents.Chatwindow", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fresources%2Fviews%2Fcomponents%2FChatWindow.blade.php&line=1", "ajax": false, "filename": "ChatWindow.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.032447, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "dress_up_davao", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'uFpT83g9VI4Qw34KX9d4QawaU99FPm2P7mn6DPy9' limit 1", "type": "query", "params": [], "bindings": ["uFpT83g9VI4Qw34KX9d4QawaU99FPm2P7mn6DPy9"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.0338778, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\dress-up-davao\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "dress_up_davao", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": null, "controller_action": "Closure", "uri": "GET /", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2Fdress-up-davao%2Froutes%2Fweb.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:17-19</a>", "middleware": "web", "duration": "502ms", "peak_memory": "38MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1048468111 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1048468111\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-515327259 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-515327259\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1911688819 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Brave&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRaM2tKMjdiNEtXNXRTNDJ6NFpNaHc9PSIsInZhbHVlIjoiTytBWVpoNmwyRnFTRXBuMmY5OVZEZzFGMU5rOU1WbWhHTi9sMkprdjBVOE5Dc0dsVFhUdXB0Lzk4Vy8va2JBR3hyaU1neENEV2dDR2YrQ2pBUkRLcmFUa0xadU1MMExxamErbDhFeVJKRytjaWpSL2NjQ0ExenhtQzZGdS81ejIiLCJtYWMiOiIxZGZmM2NlZTdmNzdkYzI2MzE1MGY4Nzc4ZDNmNzhhZDA1NDE1NzdjYTJiNmZiZWQ2ZDg5YmRmNGMzZDk5MzgzIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IjJYNUp1NlB1S1NYVFF5UFBiSk5oV3c9PSIsInZhbHVlIjoiWGhtb1dmWFh6SERHdFJ1V0NtL3hBU3I3d2Z0QlpXQzRHNXdMVzg0NFFQcEJ4SitEWnBCWWRjMzhvVnI5a1lkUlNGaGJxTzBFQndYMmpEU2RZVjlUbVY3cWsyZjk5amI5UXhVbFNHUnBBVjU0YnJNaGZ1cFVwS0VFVG1YdjgwSDIiLCJtYWMiOiI3ZDRlYjliZDJiMzViYTcyNmM5Njk1NTZmMjA2ZmJjMmQ4NDU3NTBkOWVmM2IwYmIwNTFmNTI5NGQ2YjFhMDQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911688819\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1858907534 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0R163vWDKKbOUUrPYsGNjhBMh2qB6mdUZvk72as3</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uFpT83g9VI4Qw34KX9d4QawaU99FPm2P7mn6DPy9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858907534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-958265366 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 07:55:41 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958265366\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-383591442 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0R163vWDKKbOUUrPYsGNjhBMh2qB6mdUZvk72as3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/login</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383591442\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "controller_action": "Closure"}, "badge": null}}